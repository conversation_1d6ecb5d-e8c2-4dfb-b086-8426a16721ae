on:
  push:
    branches:
      - main
  pull_request:
    types:
      - opened
      - edited
      - reopened
      - synchronize

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

name: Static checks
jobs:
  test:
    name: cargo test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: ls -l */*
      - run: ./tests/install_rust.sh
      - run: cargo test --all-features

  formatting:
    name: cargo fmt
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: ./tests/install_rust.sh
      - run: cargo fmt --all -- --check


  linting:
    name: cargo clippy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: ./tests/install_rust.sh
      - run: cargo clippy --all-features -- -D warnings

  security:
    name: cargo audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: ./tests/install_rust.sh
      - run: cargo install cargo-audit
      - run: cargo audit

  miri:
    name: cargo miri
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: ./tests/install_rust.sh nightly
      - run: cargo miri test || true # ignore errors since miri is still experimental and complains about the std toolchain