# https://github.com/marketplace/actions/shellcheck
name: Check shell scripts

on:
  workflow_dispatch:
  pull_request:
    types:
      - opened
      - edited
      - reopened
      - synchronize

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  shellcheck:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout the code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          persist-credentials: false
      - name: Run ShellCheck
        uses: ludeeus/action-shellcheck@00b27aa7cb85167568cb48a3838b75f4265f2bca # master (2024-06-20)
        with:
          ignore_paths: "**/vendor/**"
