use anyhow::{Context, Result};
use nix::unistd::{Gid, Uid};
use rand::Rng;
use std::fs::OpenOptions;
use std::io::Write;

#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub struct UserGroup {
    pub user_id: Uid,
    pub group_id: Gid,
    pub user_name: String,
    pub group_name: String,
}

impl UserGroup {
    pub fn new() -> Self {
        Self::root()
    }

    pub fn root() -> Self {
        Self {
            user_id: Uid::from_raw(0),
            group_id: Gid::from_raw(0),
            user_name: "root".into(),
            group_name: "root".into(),
        }
    }

    pub fn with_ids(uid: u32, gid: u32, user: String, group: String) -> Self {
        Self {
            user_id: Uid::from_raw(uid),
            group_id: Gid::from_raw(gid),
            user_name: user,
            group_name: group,
        }
    }

    pub fn write_to_system_files(&self) -> Result<()> {
        self.write_to_files("/etc/passwd", "/etc/shadow", "/etc/group")
    }

    pub fn write_to_files(&self, pw: &str, sh: &str, gr: &str) -> Result<()> {
        for (p, entry_fn) in [
            (pw, self.passwd_entry()),
            (sh, self.shadow_entry()),
            (gr, self.group_entry()),
        ] {
            self.append(p, &entry_fn)?;
        }
        Ok(())
    }

    fn passwd_entry(&self) -> String {
        format!(
            "{}:x:{}:{}:{}:/nonexistent:/bin/false\n",
            self.user_name, self.user_id, self.group_id, self.user_name
        )
    }

    fn shadow_entry(&self) -> String {
        format!("{}:*:18295:0:99999:7:::\n", self.user_name)
    }

    fn group_entry(&self) -> String {
        format!("{}:x:{}:\n", self.group_name, self.group_id)
    }

    fn append(&self, path: &str, content: &str) -> Result<()> {
        let mut f = OpenOptions::new()
            .create(true)
            .append(true)
            .open(path)
            .with_context(|| format!("open {path}"))?;
        f.write_all(content.as_bytes())
            .with_context(|| format!("write {path}"))?;
        Ok(())
    }
}

impl Default for UserGroup {
    fn default() -> Self {
        Self::new()
    }
}

pub fn random_user_group() -> Result<UserGroup> {
    let mut rng = rand::rng();
    let uid = rng.random_range(1000..60000);
    let gid = rng.random_range(1000..60000);
    let name: String = (0..8)
        .map(|_| rng.random_range(b'a'..=b'z') as char)
        .collect();
    let ug = UserGroup::with_ids(uid, gid, name.clone(), name);
    ug.write_to_system_files()?;
    Ok(ug)
}

#[cfg(test)]
mod tests {
    use super::*;
    use nix::unistd::Uid;
    use serial_test::serial;

    #[test]
    fn test_user_group_new() {
        let u = UserGroup::new();
        assert_eq!(u.user_id, Uid::from_raw(0));
        assert_eq!(u.group_id, nix::unistd::Gid::from_raw(0));
        assert_eq!(u.user_name, "root");
    }

    #[test]
    fn test_passwd_entry_format() {
        let u = UserGroup::with_ids(1001, 1001, "testuser".into(), "testgroup".into());
        let e = format!(
            "{}:x:{}:{}:{}:/nonexistent:/bin/false\n",
            u.user_name, u.user_id, u.group_id, u.user_name
        );
        assert_eq!(e, "testuser:x:1001:1001:testuser:/nonexistent:/bin/false\n");
        let f: Vec<&str> = e.trim_end().split(':').collect();
        assert_eq!(f.len(), 7);
    }

    #[test]
    fn test_shadow_entry_format() {
        let u = UserGroup::with_ids(1001, 1001, "testuser".into(), "testgroup".into());
        let e = format!("{}:*:18295:0:99999:7:::\n", u.user_name);
        assert_eq!(e, "testuser:*:18295:0:99999:7:::\n");
    }

    #[test]
    fn test_group_entry_format() {
        let u = UserGroup::with_ids(1001, 1001, "testuser".into(), "testgroup".into());
        let e = format!("{}:x:{}:\n", u.group_name, u.group_id);
        assert_eq!(e, "testgroup:x:1001:\n");
    }

    #[test]
    fn test_random_user_group_generation() {
        let mut rng = rand::rng();
        let uid = rng.random_range(1000..60000);
        let gid = rng.random_range(1000..60000);
        assert!(uid >= 1000 && uid < 60000);
        assert!(gid >= 1000 && gid < 60000);
        let name: String = (0..8)
            .map(|_| rng.random_range(b'a'..=b'z') as char)
            .collect();
        assert_eq!(name.len(), 8);
    }

    #[test]
    fn test_passwd_entry_edge_cases() {
        let u = UserGroup::with_ids(0, 0, "root".into(), "root".into());
        let e = format!(
            "{}:x:{}:{}:{}:/nonexistent:/bin/false\n",
            u.user_name, u.user_id, u.group_id, u.user_name
        );
        assert_eq!(e, "root:x:0:0:root:/nonexistent:/bin/false\n");
    }

    #[test]
    fn test_shadow_entry_validity() {
        let u = UserGroup::with_ids(1001, 1001, "testuser".into(), "testgroup".into());
        let e = format!("{}:*:18295:0:99999:7:::\n", u.user_name);
        assert!(e.contains(":*:"));
        assert!(e.contains(":99999:"));
        assert!(e.contains(":7:"));
    }

    #[test]
    fn test_group_entry_no_members() {
        let u = UserGroup::with_ids(1001, 1001, "testuser".into(), "testgroup".into());
        let e = format!("{}:x:{}:\n", u.group_name, u.group_id);
        assert!(e.ends_with(":\n"));
    }

    #[test]
    fn test_format_compliance_with_real_examples() {
        let u = UserGroup::with_ids(1234, 1234, "myuser".into(), "mygroup".into());
        let e = format!(
            "{}:x:{}:{}:{}:/nonexistent:/bin/false\n",
            u.user_name, u.user_id, u.group_id, u.user_name
        );
        assert_eq!(e, "myuser:x:1234:1234:myuser:/nonexistent:/bin/false\n");
        let parts: Vec<&str> = e.trim().split(':').collect();
        assert_eq!(parts.len(), 7);
        assert!(parts[2].parse::<u32>().is_ok());
    }

    #[test]
    #[serial]
    fn test_safe_file_operations() {
        let pw = tempfile::NamedTempFile::new().unwrap();
        let sh = tempfile::NamedTempFile::new().unwrap();
        let gr = tempfile::NamedTempFile::new().unwrap();
        let u = UserGroup::with_ids(9999, 9999, "testuser".into(), "testgroup".into());
        u.write_to_files(
            pw.path().to_str().unwrap(),
            sh.path().to_str().unwrap(),
            gr.path().to_str().unwrap(),
        )
        .unwrap();
        let pc = std::fs::read_to_string(pw.path()).unwrap();
        let sc = std::fs::read_to_string(sh.path()).unwrap();
        let gc = std::fs::read_to_string(gr.path()).unwrap();
        assert_eq!(
            pc,
            "testuser:x:9999:9999:testuser:/nonexistent:/bin/false\n"
        );
        assert_eq!(sc, "testuser:*:18295:0:99999:7:::\n");
        assert_eq!(gc, "testgroup:x:9999:\n");
    }

    #[test]
    fn test_user_group_with_ids() {
        let u = UserGroup::with_ids(1234, 5678, "testuser".into(), "testgroup".into());
        assert_eq!(u.user_id, Uid::from_raw(1234));
        assert_eq!(u.group_id, Gid::from_raw(5678));
    }

    #[test]
    fn test_user_group_root() {
        let r = UserGroup::root();
        assert_eq!(r.user_id, Uid::from_raw(0));
        assert_eq!(r.user_name, "root");
    }

    #[test]
    fn test_user_group_default() {
        let d = UserGroup::default();
        assert_eq!(d, UserGroup::new());
    }

    #[test]
    fn test_entry_generation() {
        let u = UserGroup::with_ids(1001, 1002, "myuser".into(), "mygroup".into());
        assert_eq!(
            u.passwd_entry(),
            "myuser:x:1001:1002:myuser:/nonexistent:/bin/false\n"
        );
        assert_eq!(u.shadow_entry(), "myuser:*:18295:0:99999:7:::\n");
        assert_eq!(u.group_entry(), "mygroup:x:1002:\n");
    }
}
