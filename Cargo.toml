[package]
name = "NVRC"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.44"
nix = { version =  "0.30.1", features = ["fs", "mount", "user", "process", "reboot", "signal", "mman", "poll", "ioctl"] }
cfg-if = "1.0"
log = "0.4"
kernlog = "0.3"
kobject-uevent = "0.2.0"
netlink-sys = "0.8.6"
lazy_static = "1.4.0"
rand = "0.9.0"
rlimit = "0.10.2"
sysinfo = "0.36.1"
libc = "0.2"
raw-cpuid = "11.5.0"

[profile.release]
opt-level = "s"
lto = true
strip = true
panic = 'abort'

[dev-dependencies]
mktemp = "0.5.1"
tempfile = "3.2.0"
serial_test = "3.2.0"

[features]
confidential = []